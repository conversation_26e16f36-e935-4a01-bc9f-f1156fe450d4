{"name": "portfolio", "version": "0.1.0", "private": true, "homepage": "https://vishal.biyani.xyz", "dependencies": {"@copilotkit/react-core": "^1.8.13", "@copilotkit/react-ui": "^1.8.13", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@mui/icons-material": "^7.1.0", "@mui/lab": "^7.0.0-beta.11", "@mui/material": "^7.1.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@toolpad/core": "^0.15.0", "aos": "^2.3.4", "bootstrap": "^5.3.5", "color": "^5.0.0", "flexsearch": "^0.8.164", "framer-motion": "^12.10.5", "fuse.js": "^7.1.0", "gh-pages": "^6.3.0", "http-proxy-middleware": "^3.0.5", "lucide-react": "^0.511.0", "pg": "^8.16.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.5.3", "react-scripts": "5.0.1", "react-scroll": "^1.9.3", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "slate": "^0.114.0", "slate-history": "^0.113.1", "slate-react": "^0.114.2", "web-vitals": "^2.1.4"}, "scripts": {"start": "PORT=3775 react-scripts start", "win": "set PORT=3775 && craco start", "build": "react-scripts build", "build:dev": "powershell -File ./build-dev.ps1", "build:prod": "powershell -File ./build-prod.ps1", "build:prod:node": "node build-prod.js", "test": "react-scripts test", "eject": "react-scripts eject", "deploy": "npm run build:prod && gh-pages -d build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:3000", "devDependencies": {"@craco/craco": "^7.1.0", "@mdx-js/loader": "^3.1.0", "dotenv": "^16.5.0"}}