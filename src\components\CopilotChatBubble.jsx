// components/CopilotChatBubble.jsx
import React, { useState, useEffect } from "react";
import { createPortal } from "react-dom";
import { CopilotKit } from "@copilotkit/react-core";
import { CopilotPopup } from "@copilotkit/react-ui";
import { Box, IconButton, Tooltip } from "@mui/material";
import ChatIcon from "@mui/icons-material/Chat";
import CloseIcon from "@mui/icons-material/Close";
import { useTheme } from "@mui/material/styles";

export default function CopilotChatBubble() {
  const theme = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleToggle = () => {
    setIsOpen(!isOpen);
  };

  if (!mounted) return null;

  return (
    <>
      {/* Chat <PERSON> */}
      <Box
        sx={{
          position: "fixed",
          bottom: 16,
          right: 16,
          zIndex: 1600,
          display: "flex",
          alignItems: "center",
        }}
      >
        <Tooltip title={isOpen ? "Close Chat" : "Ask Me Anything"} arrow>
          <IconButton
            sx={{
              backgroundColor: theme.palette.primary.main,
              color: theme.palette.primary.contrastText,
              width: 56,
              height: 56,
              borderRadius: "50%",
              boxShadow: theme.shadows[6],
              transition: "transform 0.2s ease, box-shadow 0.2s ease",
              "&:hover": {
                transform: "scale(1.1)",
                boxShadow: theme.shadows[10],
                backgroundColor: theme.palette.primary.dark,
              },
            }}
            onClick={handleToggle}
          >
            {isOpen ? <CloseIcon fontSize="medium" /> : <ChatIcon fontSize="medium" />}
          </IconButton>
        </Tooltip>
      </Box>

      {/* Chat Popup Portal */}
      {isOpen && createPortal(
        <div
          style={{
            position: "fixed",
            bottom: "80px",
            right: "16px",
            zIndex: 10000,
            width: "400px",
            height: "600px",
            maxWidth: "calc(100vw - 32px)",
            maxHeight: "calc(100vh - 100px)",
            fontFamily: "system-ui, -apple-system, sans-serif",
            backgroundColor: "#ffffff",
            border: "1px solid #e5e7eb",
            borderRadius: "12px",
            boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
            overflow: "hidden",
            isolation: "isolate",
          }}
        >
          <style>{`
            /* Reset and override CopilotKit styles */
            .copilot-popup-container * {
              box-sizing: border-box !important;
              font-family: system-ui, -apple-system, sans-serif !important;
            }
            
            .copilot-popup-container .copilotkit-popup {
              width: 100% !important;
              height: 100% !important;
              max-width: none !important;
              max-height: none !important;
              position: static !important;
              border: none !important;
              border-radius: 0 !important;
              box-shadow: none !important;
              margin: 0 !important;
              padding: 0 !important;
            }
            
            .copilot-popup-container .copilotkit-popup > div {
              width: 100% !important;
              height: 100% !important;
              display: flex !important;
              flex-direction: column !important;
            }
            
            /* Header styles */
            .copilot-popup-container [data-testid="copilot-popup-header"] {
              padding: 16px !important;
              border-bottom: 1px solid #e5e7eb !important;
              background: #ffffff !important;
              display: flex !important;
              align-items: center !important;
              justify-content: space-between !important;
              flex-shrink: 0 !important;
            }
            
            /* Messages area */
            .copilot-popup-container [data-testid="copilot-popup-messages"] {
              flex: 1 !important;
              overflow-y: auto !important;
              padding: 16px !important;
              background: #ffffff !important;
            }
            
            /* Input area */
            .copilot-popup-container [data-testid="copilot-popup-input"] {
              padding: 16px !important;
              border-top: 1px solid #e5e7eb !important;
              background: #ffffff !important;
              flex-shrink: 0 !important;
            }
            
            .copilot-popup-container textarea,
            .copilot-popup-container input {
              width: 100% !important;
              padding: 12px !important;
              border: 1px solid #d1d5db !important;
              border-radius: 8px !important;
              font-size: 14px !important;
              line-height: 1.5 !important;
              resize: none !important;
            }
            
            .copilot-popup-container textarea:focus,
            .copilot-popup-container input:focus {
              outline: none !important;
              border-color: #3b82f6 !important;
              box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
            }
            
            /* Mobile responsive */
            @media (max-width: 640px) {
              .copilot-popup-mobile {
                width: calc(100vw - 32px) !important;
                height: calc(100vh - 100px) !important;
                right: 16px !important;
                left: 16px !important;
              }
            }
          `}</style>
          
          <div 
            className={`copilot-popup-container ${window.innerWidth <= 640 ? 'copilot-popup-mobile' : ''}`}
            style={{ width: "100%", height: "100%" }}
          >
            <CopilotKit 
            runtimeUrl="http://192.168.1.137:6600/api/copilotkit"
            // publicApiKey="ck_pub_04e844a3046664d5aee8d25970d0e38f"
            >
              <CopilotPopup
                instructions="You're a helpful AI assistant guiding users through the site."
                defaultOpen={true}
                labels={{
                  title: "Chat with Vishal's Copilot",
                  initial: "Hi there! How can I help you explore this site?",
                }}
                onSetOpen={setIsOpen}
              />
            </CopilotKit>
          </div>
        </div>,
        document.body
      )}
    </>
  );
}