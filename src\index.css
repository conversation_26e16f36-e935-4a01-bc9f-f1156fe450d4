body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON> Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* CopilotKit Popup Overrides */
.copilotkit-popup {
  width: 400px !important;
  height: 600px !important;
  max-width: calc(100vw - 32px) !important;
  max-height: calc(100vh - 100px) !important;
  position: fixed !important;
  bottom: 80px !important;
  right: 16px !important;
  z-index: 10000 !important;
  border-radius: 12px !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1) !important;
  overflow: hidden !important;
}

.copilotkit-popup > div {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

.copilotkit-popup * {
  box-sizing: border-box !important;
  font-family: system-ui, -apple-system, sans-serif !important;
}

@media (max-width: 640px) {
  .copilotkit-popup {
    width: calc(100vw - 32px) !important;
    height: calc(100vh - 100px) !important;
    left: 16px !important;
    right: 16px !important;
  }
}

/* Additional CopilotKit styling tweaks for src/index.css */

/* Better header styling */
.copilotkit-popup [data-testid="copilot-popup-header"],
.copilotkit-popup header {
  background: #f8f9fa !important;
  border-bottom: 1px solid #e9ecef !important;
  padding: 12px 16px !important;
  font-weight: 600 !important;
}

/* Message area improvements */
.copilotkit-popup [data-testid="copilot-popup-messages"],
.copilotkit-popup .messages-container {
  padding: 16px !important;
  background: #ffffff !important;
  overflow-y: auto !important;
  flex: 1 !important;
}

/* Input area styling */
.copilotkit-popup [data-testid="copilot-popup-input"],
.copilotkit-popup .input-container {
  padding: 16px !important;
  border-top: 1px solid #e9ecef !important;
  background: #ffffff !important;
}

/* Input field improvements */
.copilotkit-popup textarea,
.copilotkit-popup input[type="text"] {
  border: 1px solid #ced4da !important;
  border-radius: 8px !important;
  padding: 8px 12px !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  transition: border-color 0.15s ease-in-out !important;
}

.copilotkit-popup textarea:focus,
.copilotkit-popup input[type="text"]:focus {
  border-color: #80bdff !important;
  outline: 0 !important;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

/* Button styling */
.copilotkit-popup button {
  border-radius: 6px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  transition: all 0.15s ease-in-out !important;
}

/* Close button */
.copilotkit-popup button[aria-label*="close"],
.copilotkit-popup button[aria-label*="Close"] {
  background: transparent !important;
  border: none !important;
  color: #6c757d !important;
  padding: 4px !important;
}

.copilotkit-popup button[aria-label*="close"]:hover,
.copilotkit-popup button[aria-label*="Close"]:hover {
  background: #f8f9fa !important;
  color: #495057 !important;
}

/* Send button */
.copilotkit-popup button[type="submit"] {
  background: #007bff !important;
  color: white !important;
  border: 1px solid #007bff !important;
  padding: 6px 12px !important;
}

.copilotkit-popup button[type="submit"]:hover {
  background: #0056b3 !important;
  border-color: #0056b3 !important;
}

/* Scrollbar styling */
.copilotkit-popup *::-webkit-scrollbar {
  width: 6px !important;
}

.copilotkit-popup *::-webkit-scrollbar-track {
  background: #f1f1f1 !important;
}

.copilotkit-popup *::-webkit-scrollbar-thumb {
  background: #c1c1c1 !important;
  border-radius: 3px !important;
}

.copilotkit-popup *::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8 !important;
}